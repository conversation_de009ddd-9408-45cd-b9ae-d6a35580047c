import json
from langchain_core.messages import AIMessage, HumanMessage
from .session_history import get_session_history
from .agent_base import AgentBase
from utils.logger import LOG


class DocumentAgent(AgentBase):
    """
    文书生成代理类，负责处理法律文书的智能生成。
    支持起诉状、离婚协议、劳动仲裁申请书、行政判决书等多种文书类型。
    """
    
    def __init__(self, session_id=None):
        super().__init__(
            name="document_generation",
            prompt_file="prompts/document_generation_prompt.txt",
            session_id=session_id
        )
        
        # 文书类型配置
        self.document_types = {
            'lawsuit': {
                'name': '起诉状',
                'description': '民事起诉状，用于向法院提起民事诉讼',
                'icon': 'fas fa-file-alt',
                'fields': [
                    {'name': 'plaintiff_name', 'label': '原告姓名', 'type': 'text', 'required': True},
                    {'name': 'plaintiff_gender', 'label': '原告性别', 'type': 'select', 'options': ['男', '女'], 'required': True},
                    {'name': 'plaintiff_age', 'label': '原告年龄', 'type': 'number', 'required': True},
                    {'name': 'plaintiff_address', 'label': '原告住址', 'type': 'text', 'required': True},
                    {'name': 'plaintiff_phone', 'label': '原告联系电话', 'type': 'text', 'required': True},
                    {'name': 'defendant_name', 'label': '被告姓名', 'type': 'text', 'required': True},
                    {'name': 'defendant_gender', 'label': '被告性别', 'type': 'select', 'options': ['男', '女'], 'required': True},
                    {'name': 'defendant_age', 'label': '被告年龄', 'type': 'number', 'required': True},
                    {'name': 'defendant_address', 'label': '被告住址', 'type': 'text', 'required': True},
                    {'name': 'case_facts', 'label': '案件事实', 'type': 'textarea', 'required': True},
                    {'name': 'legal_basis', 'label': '法律依据', 'type': 'textarea', 'required': True},
                    {'name': 'claims', 'label': '诉讼请求', 'type': 'textarea', 'required': True},
                    {'name': 'court_name', 'label': '受理法院', 'type': 'text', 'required': True}
                ]
            },
            'divorce_agreement': {
                'name': '离婚协议书',
                'description': '夫妻双方协议离婚时签署的协议书',
                'icon': 'fas fa-handshake',
                'fields': [
                    {'name': 'husband_name', 'label': '男方姓名', 'type': 'text', 'required': True},
                    {'name': 'husband_id', 'label': '男方身份证号', 'type': 'text', 'required': True},
                    {'name': 'husband_address', 'label': '男方住址', 'type': 'text', 'required': True},
                    {'name': 'wife_name', 'label': '女方姓名', 'type': 'text', 'required': True},
                    {'name': 'wife_id', 'label': '女方身份证号', 'type': 'text', 'required': True},
                    {'name': 'wife_address', 'label': '女方住址', 'type': 'text', 'required': True},
                    {'name': 'marriage_date', 'label': '结婚日期', 'type': 'date', 'required': True},
                    {'name': 'marriage_cert_no', 'label': '结婚证号', 'type': 'text', 'required': True},
                    {'name': 'children_info', 'label': '子女情况', 'type': 'textarea', 'required': False},
                    {'name': 'property_division', 'label': '财产分割', 'type': 'textarea', 'required': True},
                    {'name': 'debt_handling', 'label': '债务处理', 'type': 'textarea', 'required': False},
                    {'name': 'child_custody', 'label': '子女抚养', 'type': 'textarea', 'required': False}
                ]
            },
            'labor_arbitration': {
                'name': '劳动仲裁申请书',
                'description': '向劳动仲裁委员会申请仲裁的申请书',
                'icon': 'fas fa-briefcase',
                'fields': [
                    {'name': 'applicant_name', 'label': '申请人姓名', 'type': 'text', 'required': True},
                    {'name': 'applicant_gender', 'label': '申请人性别', 'type': 'select', 'options': ['男', '女'], 'required': True},
                    {'name': 'applicant_id', 'label': '申请人身份证号', 'type': 'text', 'required': True},
                    {'name': 'applicant_address', 'label': '申请人住址', 'type': 'text', 'required': True},
                    {'name': 'applicant_phone', 'label': '申请人联系电话', 'type': 'text', 'required': True},
                    {'name': 'respondent_name', 'label': '被申请人名称', 'type': 'text', 'required': True},
                    {'name': 'respondent_address', 'label': '被申请人地址', 'type': 'text', 'required': True},
                    {'name': 'respondent_representative', 'label': '被申请人法定代表人', 'type': 'text', 'required': True},
                    {'name': 'work_period', 'label': '工作期间', 'type': 'text', 'required': True},
                    {'name': 'position', 'label': '工作岗位', 'type': 'text', 'required': True},
                    {'name': 'dispute_facts', 'label': '争议事实', 'type': 'textarea', 'required': True},
                    {'name': 'arbitration_requests', 'label': '仲裁请求', 'type': 'textarea', 'required': True},
                    {'name': 'evidence_list', 'label': '证据清单', 'type': 'textarea', 'required': True}
                ]
            },
            'administrative_judgment': {
                'name': '行政判决书',
                'description': '行政诉讼案件的判决书模板',
                'icon': 'fas fa-gavel',
                'fields': [
                    {'name': 'court_name', 'label': '审理法院', 'type': 'text', 'required': True},
                    {'name': 'case_number', 'label': '案件编号', 'type': 'text', 'required': True},
                    {'name': 'plaintiff_name', 'label': '原告姓名/名称', 'type': 'text', 'required': True},
                    {'name': 'plaintiff_address', 'label': '原告住址/地址', 'type': 'text', 'required': True},
                    {'name': 'defendant_name', 'label': '被告行政机关', 'type': 'text', 'required': True},
                    {'name': 'defendant_address', 'label': '被告地址', 'type': 'text', 'required': True},
                    {'name': 'case_facts', 'label': '案件事实', 'type': 'textarea', 'required': True},
                    {'name': 'disputed_action', 'label': '争议的行政行为', 'type': 'textarea', 'required': True},
                    {'name': 'legal_analysis', 'label': '法律分析', 'type': 'textarea', 'required': True},
                    {'name': 'judgment_result', 'label': '判决结果', 'type': 'textarea', 'required': True},
                    {'name': 'judge_name', 'label': '审判员姓名', 'type': 'text', 'required': True},
                    {'name': 'judgment_date', 'label': '判决日期', 'type': 'date', 'required': True}
                ]
            }
        }
    
    def get_document_types(self):
        """获取所有支持的文书类型"""
        return [
            {
                'id': doc_id,
                'name': doc_info['name'],
                'description': doc_info['description'],
                'icon': doc_info['icon']
            }
            for doc_id, doc_info in self.document_types.items()
        ]
    
    def get_document_fields(self, document_type):
        """获取指定文书类型的字段配置"""
        if document_type in self.document_types:
            return self.document_types[document_type]['fields']
        return []
    
    def generate_document(self, document_type, form_data, session_id=None):
        """
        生成指定类型的法律文书
        
        参数:
            document_type (str): 文书类型
            form_data (dict): 表单数据
            session_id (str, optional): 会话ID
        
        返回:
            str: 生成的文书内容
        """
        if session_id is None:
            session_id = self.session_id
        
        if document_type not in self.document_types:
            raise ValueError(f"不支持的文书类型: {document_type}")
        
        doc_info = self.document_types[document_type]
        
        # 构建生成文书的提示
        prompt = f"""请根据以下信息生成一份专业的{doc_info['name']}：

文书类型：{doc_info['name']}
文书描述：{doc_info['description']}

提供的信息：
"""
        
        # 添加表单数据到提示中
        for field in doc_info['fields']:
            field_name = field['name']
            field_label = field['label']
            field_value = form_data.get(field_name, '')
            if field_value:
                prompt += f"{field_label}：{field_value}\n"
        
        prompt += """

请生成一份完整、专业、符合法律规范的文书。文书应该：
1. 格式规范，符合法律文书的标准格式
2. 内容完整，包含所有必要的法律条款
3. 语言严谨，使用专业的法律术语
4. 逻辑清晰，条理分明
5. 符合相关法律法规的要求

请直接输出文书内容，不需要额外的说明。"""
        
        # 调用基类的聊天方法生成文书
        return self.chat_with_history(prompt, session_id)
    
    def validate_form_data(self, document_type, form_data):
        """
        验证表单数据的完整性和有效性
        
        参数:
            document_type (str): 文书类型
            form_data (dict): 表单数据
        
        返回:
            tuple: (is_valid, error_messages)
        """
        if document_type not in self.document_types:
            return False, [f"不支持的文书类型: {document_type}"]
        
        doc_info = self.document_types[document_type]
        errors = []
        
        for field in doc_info['fields']:
            field_name = field['name']
            field_label = field['label']
            field_required = field.get('required', False)
            field_value = form_data.get(field_name, '')
            
            # 检查必填字段
            if field_required and not field_value:
                errors.append(f"{field_label}是必填项")
            
            # 检查字段类型
            if field_value:
                field_type = field.get('type', 'text')
                if field_type == 'number':
                    try:
                        int(field_value)
                    except ValueError:
                        errors.append(f"{field_label}必须是数字")
                elif field_type == 'select':
                    options = field.get('options', [])
                    if field_value not in options:
                        errors.append(f"{field_label}的值无效")
        
        return len(errors) == 0, errors
