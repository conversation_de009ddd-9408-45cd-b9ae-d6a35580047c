// 文书生成功能管理类
class DocumentGenerator {
    constructor() {
        this.currentDocumentType = null;
        this.currentFormData = {};
        this.generatedDocument = null;
        this.init();
    }

    // 初始化
    init() {
        this.bindEvents();
        this.loadDocumentTypes();
    }

    // 绑定事件
    bindEvents() {
        // 返回类型选择按钮
        const backBtn = document.getElementById('backToTypeSelector');
        if (backBtn) {
            backBtn.addEventListener('click', () => this.showTypeSelector());
        }

        // 表单验证按钮
        const validateBtn = document.getElementById('validateFormBtn');
        if (validateBtn) {
            validateBtn.addEventListener('click', () => this.validateForm());
        }

        // 生成文书按钮
        const generateBtn = document.getElementById('generateDocumentBtn');
        if (generateBtn) {
            generateBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.generateDocument();
            });
        }

        // 重新编辑按钮
        const editBtn = document.getElementById('editDocumentBtn');
        if (editBtn) {
            editBtn.addEventListener('click', () => this.showForm());
        }

        // 下载文书按钮
        const downloadBtn = document.getElementById('downloadDocumentBtn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadDocument());
        }

        // 复制内容按钮
        const copyBtn = document.getElementById('copyDocumentBtn');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => this.copyDocument());
        }
    }

    // 加载文书类型
    async loadDocumentTypes() {
        try {
            showLoading(true);
            const response = await api.getDocumentTypes();
            
            if (response.success) {
                this.renderDocumentTypes(response.data);
            } else {
                ErrorHandler.show('获取文书类型失败: ' + response.error);
            }
        } catch (error) {
            console.error('加载文书类型失败:', error);
            ErrorHandler.show('加载文书类型失败: ' + error.message);
        } finally {
            showLoading(false);
        }
    }

    // 渲染文书类型
    renderDocumentTypes(documentTypes) {
        const container = document.getElementById('documentTypes');
        if (!container) return;

        container.innerHTML = documentTypes.map(type => `
            <div class="document-type-card" data-type="${type.id}">
                <i class="${type.icon}"></i>
                <h4>${type.name}</h4>
                <p>${type.description}</p>
            </div>
        `).join('');

        // 绑定点击事件
        container.querySelectorAll('.document-type-card').forEach(card => {
            card.addEventListener('click', () => {
                const documentType = card.dataset.type;
                this.selectDocumentType(documentType);
            });
        });
    }

    // 选择文书类型
    async selectDocumentType(documentType) {
        try {
            showLoading(true);
            this.currentDocumentType = documentType;
            
            // 获取字段配置
            const response = await api.getDocumentFields(documentType);
            
            if (response.success) {
                this.renderForm(response.data.fields);
                this.showForm();
            } else {
                ErrorHandler.show('获取表单配置失败: ' + response.error);
            }
        } catch (error) {
            console.error('选择文书类型失败:', error);
            ErrorHandler.show('选择文书类型失败: ' + error.message);
        } finally {
            showLoading(false);
        }
    }

    // 渲染表单
    renderForm(fields) {
        const container = document.getElementById('documentFormFields');
        const titleElement = document.getElementById('documentFormTitle');
        
        if (!container || !titleElement) return;

        // 更新标题
        const documentTypeName = this.getDocumentTypeName(this.currentDocumentType);
        titleElement.textContent = `填写${documentTypeName}信息`;

        // 生成表单字段
        container.innerHTML = fields.map(field => {
            const isRequired = field.required ? 'required' : '';
            const labelClass = field.required ? 'required' : '';
            
            let inputHtml = '';
            
            switch (field.type) {
                case 'textarea':
                    inputHtml = `<textarea name="${field.name}" placeholder="请输入${field.label}" ${isRequired}></textarea>`;
                    break;
                case 'select':
                    const options = field.options.map(option => `<option value="${option}">${option}</option>`).join('');
                    inputHtml = `<select name="${field.name}" ${isRequired}>
                        <option value="">请选择${field.label}</option>
                        ${options}
                    </select>`;
                    break;
                case 'date':
                    inputHtml = `<input type="date" name="${field.name}" ${isRequired}>`;
                    break;
                case 'number':
                    inputHtml = `<input type="number" name="${field.name}" placeholder="请输入${field.label}" ${isRequired}>`;
                    break;
                default:
                    inputHtml = `<input type="text" name="${field.name}" placeholder="请输入${field.label}" ${isRequired}>`;
            }

            return `
                <div class="form-group">
                    <label class="${labelClass}">${field.label}</label>
                    ${inputHtml}
                    <div class="error-message" style="display: none;"></div>
                </div>
            `;
        }).join('');
    }

    // 获取文书类型名称
    getDocumentTypeName(documentType) {
        const typeNames = {
            'lawsuit': '起诉状',
            'divorce_agreement': '离婚协议书',
            'labor_arbitration': '劳动仲裁申请书',
            'administrative_judgment': '行政判决书'
        };
        return typeNames[documentType] || '法律文书';
    }

    // 显示类型选择器
    showTypeSelector() {
        document.getElementById('documentTypeSelector').style.display = 'block';
        document.getElementById('documentFormContainer').style.display = 'none';
        document.getElementById('documentPreviewContainer').style.display = 'none';
    }

    // 显示表单
    showForm() {
        document.getElementById('documentTypeSelector').style.display = 'none';
        document.getElementById('documentFormContainer').style.display = 'block';
        document.getElementById('documentPreviewContainer').style.display = 'none';
    }

    // 显示预览
    showPreview() {
        document.getElementById('documentTypeSelector').style.display = 'none';
        document.getElementById('documentFormContainer').style.display = 'none';
        document.getElementById('documentPreviewContainer').style.display = 'block';
    }

    // 收集表单数据
    collectFormData() {
        const form = document.getElementById('documentForm');
        if (!form) return {};

        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value.trim();
        }
        
        return data;
    }

    // 验证表单
    async validateForm() {
        try {
            const formData = this.collectFormData();
            
            showLoading(true);
            const response = await api.validateDocumentForm(this.currentDocumentType, formData);
            
            if (response.success) {
                if (response.data.is_valid) {
                    ErrorHandler.show('表单验证通过！', 'success');
                    this.clearFormErrors();
                } else {
                    this.showFormErrors(response.data.errors);
                    ErrorHandler.show('表单验证失败，请检查必填项');
                }
            } else {
                ErrorHandler.show('验证失败: ' + response.error);
            }
        } catch (error) {
            console.error('验证表单失败:', error);
            ErrorHandler.show('验证表单失败: ' + error.message);
        } finally {
            showLoading(false);
        }
    }

    // 生成文书
    async generateDocument() {
        try {
            const formData = this.collectFormData();
            this.currentFormData = formData;
            
            showLoading(true);
            const sessionId = sessionManager.getSessionId('document');
            const response = await api.generateDocument(this.currentDocumentType, formData, sessionId);
            
            if (response.success) {
                this.generatedDocument = response.data.document_content;
                this.renderPreview(this.generatedDocument);
                this.showPreview();
                ErrorHandler.show('文书生成成功！', 'success');
            } else {
                if (response.validation_errors) {
                    this.showFormErrors(response.validation_errors);
                }
                ErrorHandler.show('生成文书失败: ' + response.error);
            }
        } catch (error) {
            console.error('生成文书失败:', error);
            ErrorHandler.show('生成文书失败: ' + error.message);
        } finally {
            showLoading(false);
        }
    }

    // 渲染预览
    renderPreview(documentContent) {
        const previewElement = document.getElementById('documentPreview');
        if (previewElement) {
            previewElement.textContent = documentContent;
        }
    }

    // 显示表单错误
    showFormErrors(errors) {
        this.clearFormErrors();
        
        errors.forEach(error => {
            // 这里可以根据具体的错误信息来定位到具体的字段
            // 暂时显示通用错误
            console.warn('表单错误:', error);
        });
    }

    // 清除表单错误
    clearFormErrors() {
        const errorElements = document.querySelectorAll('.form-group .error-message');
        errorElements.forEach(element => {
            element.style.display = 'none';
            element.textContent = '';
        });
        
        const formGroups = document.querySelectorAll('.form-group');
        formGroups.forEach(group => {
            group.classList.remove('error');
        });
    }

    // 下载文书
    downloadDocument() {
        if (!this.generatedDocument) {
            ErrorHandler.show('没有可下载的文书');
            return;
        }

        const documentTypeName = this.getDocumentTypeName(this.currentDocumentType);
        const filename = `${documentTypeName}_${new Date().toISOString().split('T')[0]}.txt`;
        
        const blob = new Blob([this.generatedDocument], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        ErrorHandler.show('文书下载成功！', 'success');
    }

    // 复制文书内容
    async copyDocument() {
        if (!this.generatedDocument) {
            ErrorHandler.show('没有可复制的文书');
            return;
        }

        try {
            await navigator.clipboard.writeText(this.generatedDocument);
            ErrorHandler.show('文书内容已复制到剪贴板！', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            ErrorHandler.show('复制失败，请手动选择文本复制');
        }
    }
}

// 创建全局文书生成器实例
let documentGenerator = null;

// 初始化文书生成功能
function initDocumentGenerator() {
    if (!documentGenerator) {
        documentGenerator = new DocumentGenerator();
    }
}
